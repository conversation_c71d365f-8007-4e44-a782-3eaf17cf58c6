# FCC Service Configuration
# 前庭控制系统配置文件

# HTTP服务器配置
http:
  addr: ":8081"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 60

# 数据库配置
database:
  host: "*************"
  port: 5432
  database: "fcc"
  username: "postgres"
  password: "zB3!lL9+wJ"  # 通过环境变量 FCC_DATABASE_PASSWORD 设置
  max_open_conns: 50    # 🔧 增加连接池大小，支持高并发
  max_idle_conns: 20    # 🔧 增加空闲连接数
  ssl_mode: "disable"
  timezone: "Asia/Jakarta"  # 🆕 数据库时区配置，支持全球化部署

# 🔧 Redis 缓存配置 - 支持开关控制
redis:
  enabled: false  # 🔧 禁用Redis，使用内存缓存作为降级方案
  host: "localhost"
  port: 6379
  password: ""
  db: 0

# DART协议配置
dart:
  # 串口通信配置
  serial:
    device_patterns:
      - "/dev/ttyUSB*"
      - "/dev/ttyACM*"
      - "/dev/ttyS*"
    baud_rate: 9600    # 支持9600/19200自适应
    data_bits: 8
    stop_bits: 1
    parity: "N"        # N=无校验, E=偶校验, O=奇校验
    timeout: 1000ms    # 修复：添加时间单位
    enable_raw_data_logging: true   # 启用原始数据调试日志(仅debug模式有效)

  # 设备发现配置
  discovery:
    scan_interval: 30s          # 修复：添加时间单位
    address_range_min: 0x50     # 最小地址(80)
    address_range_max: 0x6F     # 最大地址(111)
    probe_timeout: 100ms        # 修复：添加时间单位
    retry_count: 3              # 重试次数

  # 协议参数配置 - 设备轮询错开优化
  protocol:
    response_timeout: 1000ms    # 🔧 优化：增加至1000ms，适应设备错开轮询
    max_retries: 0              # 🚀 优化：禁用重试，避免时间窗口混乱
    dle_enabled: true           # 启用DLE透明处理
    crc_enabled: true           # 启用CRC-16校验

# FCC架构集成配置
fcc:
  # 设备管理服务集成
  device_management:
    endpoint: "http://localhost:8081"
    timeout: 5000ms            # 修复：添加时间单位
    retry_count: 3
    auth_token: ""             # 通过环境变量设置

  # 业务服务集成
  business_services:
    endpoint: "http://localhost:8082"
    timeout: 5000ms            # 修复：添加时间单位
    retry_count: 3
    auth_token: ""             # 通过环境变量设置

  # API网关配置
  api_gateway:
    endpoint: "http://localhost:8000"
    timeout: 3000ms            # 修复：添加时间单位
    auth_token: ""             # 通过环境变量设置

# 监控配置
monitoring:
  enabled: true
  metrics_port: 9090
  metrics_path: "/metrics"

# 日志配置
logging:
  level: "debug"              # 主应用日志级别
  format: "json"             # json, text
  console:
    enabled: true            # 启用控制台输出
  file:
    enabled: true            # 启用文件输出
    path: "logs/fcc-service.log"  # 日志文件路径
    max_size: 100            # 单个日志文件最大大小(MB)
    max_backups: 10          # 保留的备份文件数量
    max_age: 30              # 保留日志文件天数
    compress: true           # 是否压缩旧的日志文件

  # 🚀 通讯日志分离配置 - 专门用于传输层通讯数据
  communication:
    enabled: true            # 启用通讯日志分离
    level: "debug"           # 通讯日志级别（通常需要debug级别查看详细通讯数据）
    format: "json"           # 通讯日志格式
    console:
      enabled: false         # 通讯日志不输出到控制台（避免干扰）
    file:
      enabled: true          # 启用通讯日志文件输出
      path: "logs/fcc-communication.log"  # 通讯日志专用文件
      max_size: 50           # 更小的文件大小（通讯日志量大）
      max_backups: 20        # 更多备份文件（保留更多历史）
      max_age: 7             # 更短保留期（7天，通讯日志主要用于调试）
      compress: true         # 压缩旧文件（节省空间）

# 业务配置
business:
  # 泵控制参数
  pump:
    max_concurrent_operations: 32    # 最大并发操作数
    authorization_timeout: 300s      # 修复：添加时间单位
    transaction_timeout: 1800s       # 修复：添加时间单位
    
  # 数据同步配置
  sync:
    status_interval: 5s              # 修复：添加时间单位
    batch_size: 50                   # 批量处理大小
    retry_interval: 30s              # 修复：添加时间单位
    
  # 缓存配置
  cache:
    device_status_ttl: 300           # 设备状态缓存TTL(秒)
    transaction_ttl: 3600            # 交易数据缓存TTL(秒)
    max_memory: "100MB"              # 最大内存使用

  # 交易同步配置
  transaction_sync:
    enabled: true
    endpoint: "http://*************:8080/api/v1/fuel-transactions" # 实际的燃油交易API地址
    timeout: 10s
    api_key: "fcc-secret-api-key" # 简单的API密钥认证

# 安全配置
security:
  # API访问控制
  api:
    rate_limit: 1000                 # 每分钟请求限制
    max_connections: 100             # 最大连接数
    
  # 设备访问控制
  device:
    allowed_addresses:               # 允许的设备地址范围
      - "0x50-0x6F"
    authentication_required: false   # 是否需要设备认证
    
# 错误处理配置
error_handling:
  # 重试策略
  retry:
    max_attempts: 3
    base_delay: 100ms                # 修复：添加时间单位
    max_delay: 5000ms                # 修复：添加时间单位
    multiplier: 2                    # 延迟倍数
    
  # 断路器配置
  circuit_breaker:
    failure_threshold: 5             # 失败阈值
    recovery_timeout: 30s            # 修复：添加时间单位
    half_open_max_calls: 3           # 半开状态最大调用数

adapters:
  wayne:
    type: "wayne_dart"
    connection:
      serial_port: "COM7"
      baud_rate: 9600
      timeout: 5s
      max_retries: 3
    # DART协议设备发现优化配置 - 大幅降低发送频率
    options:
      # 设备发现批次大小 (1-4, 默认1)
      discovery_batch_size: 1
      # 批次间延迟毫秒数 (默认2000ms)
      discovery_batch_delay_ms: 2000
      # 地址间延迟毫秒数 (默认500ms)
      discovery_address_delay_ms: 500
      # 设备响应超时毫秒数 (默认1000ms)
      discovery_response_timeout_ms: 1000
      # 🚀 增强轮询功能配置 (MVP) - 默认开启以获取更全面的设备数据
      enhanced_polling_enabled: true  # 是否启用增强轮询功能，默认开启 

# 🚀 串口调度器配置 - 时分复用通信方案（阶段3最优化配置）
serial_scheduler:
  enabled: true             # 启用串口调度器
  time_slice_ms: 2000       # 每个设备独占2秒时间片 - 最优化配置
  switch_delay_ms: 200      # 设备切换后强制静默200ms - 最小静默期
  max_wait_timeout_ms: 3000 # 最大等待6秒（一个完整周期：2s+2s+0.4s静默） 

# 看门狗配置 - 优化授权流程保护
watchdog:
  enabled: true                    # 启用看门狗机制
  default_timeout: 30s             # 默认看门狗超时（从10s增加到30s）
  business_critical_timeout: 60s   # 业务关键状态（授权/加油）超时
  ping_interval: 1s                # ping间隔时间
  max_timeout_count: 5             # 最大超时次数 

# 预授权配置
preauth:
  enabled: true                   # 是否启用预授权模式
  default_ttl: 35s                 # 预授权缓存默认过期时间

# 自动授权配置  
autoauth:
  enabled: false                   # 是否启用自动授权模式 